# Environment Configuration (Example)
NODE_ENV=production
PORT=3000

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/whatsapp-api

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./sessions
QR_CODE_TIMEOUT=60000

# Session Management (Memory Optimization)
MAX_ACTIVE_SESSIONS=100          # Maximum concurrent sessions
MAX_RAM_USAGE_MB=10240          # Max RAM usage in MB (default: 10GB)
SESSION_CLEANUP_INTERVAL=300000  # Cleanup interval in ms (5 minutes)

# API Configuration
API_PREFIX=api/v1
